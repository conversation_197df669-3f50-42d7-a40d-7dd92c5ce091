"use client"

import React from "react"
import { cn } from "@/lib/utils"

interface GlassmorphismProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
}

export function Glassmorphism({ children, className, ...props }: GlassmorphismProps) {
  return (
    <div 
      className={cn(
        "bg-white/5 backdrop-blur-md border border-white/10 shadow-xl shadow-glass-float rounded-xl p-6",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Example usage:
export function GlassmorphismExample() {
  return (
    <div className="relative min-h-[300px] bg-gradient-to-br from-primary via-secondary to-accent p-10 flex items-center justify-center">
      <Glassmorphism className="max-w-md">
        <h3 className="text-2xl font-bold text-white mb-4">Glassmorphism Effect</h3>
        <p className="text-white/80">
          This component demonstrates a modern glassmorphism effect with semi-transparent background,
          backdrop blur, subtle borders, and custom inner shadows for a floating panel appearance.
        </p>
      </Glassmorphism>
    </div>
  )
}
