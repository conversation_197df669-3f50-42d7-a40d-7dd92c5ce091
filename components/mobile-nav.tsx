"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Sheet<PERSON>rigger } from "@/components/ui/sheet"
import { But<PERSON> } from "@/components/ui/button"
import { Menu } from "lucide-react"
import { ScrollLink } from "@/components/scroll-link"

interface MobileNavProps {
  isHomePage?: boolean
}

export function MobileNav({ isHomePage = false }: MobileNavProps) {
  const [open, setOpen] = useState(false)

  const NavLink = isHomePage ? ScrollLink : Link

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className="md:hidden">
          <Menu className="h-4 w-4" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left">
        <nav className="flex flex-col space-y-4 mt-6">
          {isHomePage ? (
            <>
              <ScrollLink
                href="#"
                className="text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setOpen(false)}
              >
                Home
              </ScrollLink>
              <ScrollLink
                href="#chi-siamo"
                className="text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setOpen(false)}
              >
                Chi Siamo
              </ScrollLink>
              <ScrollLink
                href="#aziende"
                className="text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setOpen(false)}
              >
                Le Nostre Aziende
              </ScrollLink>
              <ScrollLink
                href="#contatti"
                className="text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setOpen(false)}
              >
                Contatti
              </ScrollLink>
            </>
          ) : (
            <>
              <Link
                href="/"
                className="text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setOpen(false)}
              >
                Home
              </Link>
              <Link
                href="/#chi-siamo"
                className="text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setOpen(false)}
              >
                Chi Siamo
              </Link>
              <Link
                href="/#aziende"
                className="text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setOpen(false)}
              >
                Le Nostre Aziende
              </Link>
              <Link
                href="/#contatti"
                className="text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setOpen(false)}
              >
                Contatti
              </Link>
            </>
          )}
          <Link
            href="/lavora-con-noi"
            className="inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
            onClick={() => setOpen(false)}
          >
            Lavora con noi
          </Link>
        </nav>
      </SheetContent>
    </Sheet>
  )
}

