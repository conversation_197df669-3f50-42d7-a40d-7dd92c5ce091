"use client"

import { useTransition } from "react"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { PrivacyPolicyDialog } from "@/components/privacy-policy-dialog"
import { sendEmail } from "@/app/actions"

export function ContactForm() {
  const [isPending, startTransition] = useTransition()
  const { toast } = useToast()

  async function handleSubmit(formData: FormData) {
    startTransition(async () => {
      const result = await sendEmail(formData)

      if (result.error) {
        toast({
          variant: "destructive",
          title: "Errore",
          description: result.error,
        })
        return
      }

      toast({
        title: "Messaggio inviato",
        description: "Grazie per averci contattato. Ti risponderemo il prima possibile.",
      })

      // Reset form
      const form = document.getElementById("contact-form") as HTMLFormElement
      form.reset()
    })
  }

  return (
    <div className="glass-panel p-6 shadow-lg relative overflow-hidden">
      <form id="contact-form" action={handleSubmit} className="space-y-5 relative z-10">
        <div className="space-y-2">
          <Label htmlFor="name" className="text-lg font-medium">Nome</Label>
          <Input 
            id="name" 
            name="name" 
            required 
            placeholder="Il tuo nome" 
            minLength={2} 
            maxLength={100}
            className="h-12 rounded-lg text-lg" 
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email" className="text-lg font-medium">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            required
            placeholder="La tua email"
            pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
            className="h-12 rounded-lg text-lg"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="message" className="text-lg font-medium">Messaggio</Label>
          <Textarea
            id="message"
            name="message"
            required
            placeholder="Scrivi il tuo messaggio..."
            className="min-h-[150px] rounded-lg text-lg"
            minLength={10}
            maxLength={1000}
          />
        </div>
        <div className="flex items-center gap-2">
          <Checkbox id="privacy" name="privacy" required />
          <Label htmlFor="privacy" className="text-sm leading-snug flex-1">
            Dichiaro di aver letto e accettato la <span className="underline"><PrivacyPolicyDialog /></span>
          </Label>
        </div>
        <Button 
          type="submit" 
          className="w-full h-14 text-lg font-medium rounded-full gradient-primary" 
          disabled={isPending}
        >
          {isPending ? "Invio in corso..." : "Invia messaggio"}
        </Button>
      </form>
    </div>
  )
}
