import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import Image from "next/image"
import { Building2, Car, Code, Mail, Phone, Lightbulb, Leaf, Award, Shield } from "lucide-react"
import { PrivacyPolicyDialog } from "@/components/privacy-policy-dialog"
import { ContactForm } from "@/components/contact-form"
// import { ThemeToggle } from "@/components/theme-toggle"
import { ScrollLink } from "@/components/scroll-link"
// import { MobileNav } from "@/components/mobile-nav"
import { Button } from "@/components/ui/button"

export const metadata: Metadata = {
  title: "Exor Holding | Costruzioni, Noleggio Auto e Servizi PA",
  description:
    "Exor Holding è il punto di riferimento nei settori edilizia, mobilità e servizi per la Pubblica Amministrazione.",
  openGraph: {
    title: "Exor Holding | Costruzioni, Noleggio Auto e Servizi PA",
    description:
      "Exor Holding è il punto di riferimento nei settori edilizia, mobilità e servizi per la Pubblica Amministrazione.",
    type: "website",
    url: "https://exorholding.it",
  },
}

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 w-full border-b-0 bg-background/70 backdrop-blur-lg supports-[backdrop-filter]:bg-background/40">
        <div className="container flex h-20 items-center justify-between max-w-7xl mx-auto px-8">
          <div className="flex items-center">
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/EXOR_2-ELOOAEpGaBWbNAreZFWzwJNPwZZQTJ.png"
              alt="EXOR HOLDING"
              width={140}
              height={46}
              className="h-10 w-auto transition-apple hover:opacity-80"
            />
          </div>
          <nav className="hidden md:flex items-center gap-8">
            <ScrollLink href="#" className="text-base font-medium transition-apple hover:text-primary">
              Home
            </ScrollLink>
            <ScrollLink href="#chi-siamo" className="text-base font-medium transition-apple hover:text-primary">
              Chi Siamo
            </ScrollLink>
            <ScrollLink href="#aziende" className="text-base font-medium transition-apple hover:text-primary">
              Le Nostre Aziende
            </ScrollLink>
            <ScrollLink href="#contatti" className="text-base font-medium transition-apple hover:text-primary">
              Contatti
            </ScrollLink>

          </nav>
          <div className="flex md:hidden items-center">
            {/* Pulsante mobile rimosso */}
          </div>
        </div>
      </header>
      <main className="flex-1">
        <section className="relative w-full py-16 md:py-28 lg:py-36 xl:py-52">
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/buildings-bw.jpg"
              alt="Exor Holding Hero Background"
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/40" />
          </div>
          <div className="container relative z-10 text-center max-w-6xl mx-auto px-8">
            <div className="mx-auto max-w-4xl space-y-6">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                Costruiamo il Futuro, Un'Impresa alla Volta
              </h1>
              <p className="mx-auto max-w-[700px] text-white/90 text-xl md:text-2xl font-light leading-relaxed">
                Exor Holding è il punto di riferimento nei settori edilizia, mobilità e servizi per la PA.
              </p>
              <div className="flex justify-center pt-8">
                <Link
                  href="#aziende"
                  className="inline-flex h-14 items-center justify-center rounded-full bg-white/90 px-10 py-4 text-lg font-medium text-foreground shadow-md transition-apple hover:bg-white hover:shadow-lg focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                >
                  Scopri le nostre aziende
                </Link>
              </div>
            </div>
          </div>
        </section>

        <section id="chi-siamo" className="w-full py-16 md:py-28 lg:py-36 bg-muted/30">
          <div className="container px-8 md:px-8 max-w-7xl mx-auto">
            <div className="max-w-4xl mx-auto text-center">
              <div className="space-y-6">
                <div className="inline-block rounded-full bg-primary/10 px-4 py-1.5 text-sm font-medium text-primary">Chi Siamo</div>
                <h2 className="text-3xl font-bold tracking-tight md:text-4xl/tight lg:text-5xl/tight">
                  La nostra missione è creare valore attraverso l'eccellenza
                </h2>
                <p className="mx-auto text-muted-foreground text-lg md:text-xl/relaxed leading-relaxed">
                  Exor Holding riunisce aziende leader nei settori delle costruzioni, del noleggio auto e dei servizi
                  per la Pubblica Amministrazione. Fondata su valori di innovazione, affidabilità e sostenibilità, la
                  nostra holding si impegna a fornire soluzioni di alta qualità che rispondono alle esigenze in continua
                  evoluzione del mercato.
                </p>
                <p className="mx-auto text-muted-foreground text-lg leading-relaxed">
                  Con un approccio orientato al cliente e un forte impegno verso l'eccellenza operativa, le nostre
                  aziende lavorano in sinergia per offrire servizi integrati che generano valore per tutti i nostri
                  stakeholder.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section id="aziende" className="w-full py-16 md:py-28 lg:py-36">
          <div className="container px-8 md:px-8 max-w-7xl mx-auto">
            <div className="flex flex-col items-center justify-center space-y-6 text-center">
              <div className="space-y-4">
                <div className="inline-block rounded-full bg-primary/10 px-4 py-1.5 text-sm font-medium text-primary">
                  Le Nostre Aziende
                </div>
                <h2 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl">Eccellenza in ogni settore</h2>
                <p className="mx-auto max-w-[800px] text-muted-foreground text-lg md:text-xl/relaxed leading-relaxed">
                  Scopri le aziende che compongono Exor Holding e le soluzioni innovative che offrono.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-6xl gap-10 pt-16 md:grid-cols-3">
              <div className="flip-card-container">
                <div className="flip-card">
                  <div className="flip-card-front rounded-3xl border-0 bg-background/80 p-10 shadow-lg backdrop-blur-sm flex items-center justify-center">
                    <div className="relative w-full max-w-[280px] aspect-[3/2] overflow-hidden">
                      <Image
                        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-6036nNkRqT8w8um4CX9ngnoUteZmfI.png"
                        alt="Logo Edil Aurora Immobiliare"
                        fill
                        className="object-contain p-2"
                      />
                    </div>
                  </div>
                  <div className="flip-card-back rounded-3xl border-0 bg-background/80 p-10 shadow-lg backdrop-blur-sm">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-8 w-8 text-primary" />
                        <h3 className="text-2xl font-bold">Edil Aurora Immobiliare</h3>
                      </div>
                      <p className="text-muted-foreground text-lg leading-relaxed">
                        Esperienza e qualità nel settore edilizio, con soluzioni innovative e sostenibili. Leader nelle
                        costruzioni e sviluppo immobiliare.
                      </p>
                    </div>
                    <Link
                      href="https://www.edilauroraimmobiliare.it/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-6 inline-flex h-14 w-full items-center justify-center rounded-full border border-input/30 bg-black text-white px-8 text-lg font-medium shadow-sm transition-apple hover:bg-black/80 hover:shadow-md"
                    >
                      Visita il sito
                    </Link>
                  </div>
                </div>
              </div>

              <div className="flip-card-container">
                <div className="flip-card">
                  <div className="flip-card-front rounded-3xl border-0 bg-background/80 p-10 shadow-lg backdrop-blur-sm flex items-center justify-center">
                    <div className="relative w-full max-w-[280px] aspect-[3/2] overflow-hidden">
                      <Image
                        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Asset%201%4072x-5eQUsvG7PFVZDLNeggEJbcg0nKeNfU.png"
                        alt="Logo Vivarent"
                        fill
                        className="object-contain p-2"
                      />
                    </div>
                  </div>
                  <div className="flip-card-back rounded-3xl border-0 bg-background/80 p-10 shadow-lg backdrop-blur-sm">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Car className="h-8 w-8 text-primary" />
                        <h3 className="text-2xl font-bold">Vivarent</h3>
                      </div>
                      <p className="text-muted-foreground text-lg leading-relaxed">
                        Noleggio flessibile per privati e aziende, con soluzioni su misura. Flotta moderna e servizio
                        clienti d'eccellenza.
                      </p>
                    </div>
                    <Link
                      href="https://www.vivarent.it"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-6 inline-flex h-14 w-full items-center justify-center rounded-full border border-input/30 bg-black text-white px-8 text-lg font-medium shadow-sm transition-apple hover:bg-black/80 hover:shadow-md"
                    >
                      Visita il sito
                    </Link>
                  </div>
                </div>
              </div>



              <div className="flip-card-container">
                <div className="flip-card">
                  <div className="flip-card-front rounded-3xl border-0 bg-background/80 p-10 shadow-lg backdrop-blur-sm flex items-center justify-center">
                    <div className="relative w-full max-w-[280px] aspect-[3/2] overflow-hidden">
                      <Image
                        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo-dark-1eOKEeWhnm3Te1GzAAi0ubjUvmwFMq.png"
                        alt="Logo Transigo"
                        fill
                        className="object-contain p-2"
                      />
                    </div>
                  </div>
                  <div className="flip-card-back rounded-3xl border-0 bg-background/80 p-10 shadow-lg backdrop-blur-sm">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Mail className="h-8 w-8 text-primary" />
                        <h3 className="text-2xl font-bold">Transigo</h3>
                      </div>
                      <p className="text-muted-foreground text-lg leading-relaxed">
                        Gestione efficiente delle multe per società di noleggio auto, con soluzioni automatizzate e
                        conformi alla normativa vigente.
                      </p>
                    </div>
                    <Link
                      href="https://transigo.it"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-6 inline-flex h-14 w-full items-center justify-center rounded-full border border-input/30 bg-black text-white px-8 text-lg font-medium shadow-sm transition-apple hover:bg-black/80 hover:shadow-md"
                    >
                      Visita il sito
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section
          id="valori"
          className="w-full py-12 md:py-24 lg:py-32 bg-white relative overflow-hidden"
        >
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">I Nostri Valori</h2>
                <p className="mx-auto max-w-[800px] text-muted-foreground text-lg md:text-xl/relaxed leading-relaxed">
                  Principi fondamentali che guidano ogni nostra decisione e azione.
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
              {/* Valore 1: Innovazione */}
              <div className="flex flex-col items-center text-center p-6 border border-border rounded-xl transition-all hover:shadow-lg">
                <div className="p-4 rounded-full bg-secondary mb-4">
                  <Lightbulb className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Innovazione</h3>
                <p className="text-muted-foreground">
                  Ricerchiamo costantemente soluzioni all'avanguardia per anticipare le esigenze del mercato e dei nostri clienti.
                </p>
              </div>
              
              {/* Valore 2: Sostenibilità */}
              <div className="flex flex-col items-center text-center p-6 border border-border rounded-xl transition-all hover:shadow-lg">
                <div className="p-4 rounded-full bg-secondary mb-4">
                  <Leaf className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Sostenibilità</h3>
                <p className="text-muted-foreground">
                  Adottiamo pratiche responsabili che rispettano l'ambiente e contribuiscono al benessere delle comunità in cui operiamo.
                </p>
              </div>
              
              {/* Valore 3: Eccellenza */}
              <div className="flex flex-col items-center text-center p-6 border border-border rounded-xl transition-all hover:shadow-lg">
                <div className="p-4 rounded-full bg-secondary mb-4">
                  <Award className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Eccellenza</h3>
                <p className="text-muted-foreground">
                  Perseguiamo i più alti standard qualitativi in ogni aspetto del nostro lavoro, dalla progettazione all'esecuzione.
                </p>
              </div>
              
              {/* Valore 4: Integrità */}
              <div className="flex flex-col items-center text-center p-6 border border-border rounded-xl transition-all hover:shadow-lg">
                <div className="p-4 rounded-full bg-secondary mb-4">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Integrità</h3>
                <p className="text-muted-foreground">
                  Operiamo con trasparenza, onestà e rispetto in tutte le nostre relazioni professionali e commerciali.
                </p>
              </div>
            </div>
            

          </div>
        </section>

        <section
          id="contatti"
          className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-br from-black via-[#333333] to-[#666666] light-grid-overlay relative overflow-hidden"
        >
          <div className="container grid items-center gap-8 px-4 md:px-6 lg:grid-cols-2 lg:gap-12 relative z-10">
            <div className="space-y-4">
              <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight text-white">
                Collabora con Exor Holding per un futuro di innovazione e affidabilità.
              </h2>
              <p className="max-w-[600px] text-white/90 md:text-xl/relaxed">
                Siamo sempre alla ricerca di nuove opportunità di collaborazione e partnership strategiche.
              </p>
              <div className="mt-6 flex flex-col space-y-3 text-white">
                <p className="flex items-center gap-3 text-lg">
                  <Mail className="h-5 w-5" />
                  <EMAIL>
                </p>
                <p className="flex items-center gap-3 text-lg">
                  <Phone className="h-5 w-5" />
                  +39 0776 1810240
                </p>
              </div>
            </div>
            <div className="lg:pl-8">
              <ContactForm />
            </div>
          </div>
        </section>
      </main>
      <footer className="w-full border-t bg-muted/50 py-6">
        <div className="container flex flex-col gap-4 md:flex-row md:items-center">
          <div className="flex items-center">
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/EXOR_2-ELOOAEpGaBWbNAreZFWzwJNPwZZQTJ.png"
              alt="EXOR HOLDING"
              width={120}
              height={40}
              className="h-8 w-auto"
            />
          </div>
          <nav className="flex gap-4 md:gap-6 md:ml-auto">
            <ScrollLink href="#chi-siamo" className="text-xs hover:underline underline-offset-4">
              Chi Siamo
            </ScrollLink>
            <ScrollLink href="#contatti" className="text-xs hover:underline underline-offset-4">
              Contatti
            </ScrollLink>
            <PrivacyPolicyDialog />
          </nav>

          <p className="text-xs text-muted-foreground md:ml-auto">
            &copy; {new Date().getFullYear()} Exor Holding. Tutti i diritti riservati.
          </p>
        </div>
      </footer>
    </div>
  )
}
