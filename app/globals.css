@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-regular-webfont.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

body {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.2px;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Typography specifications */
  h1, .h1 {
    font-size: 48px;
    line-height: 1.1;
    font-weight: 800;
    letter-spacing: -0.2px;
  }
  
  @media (min-width: 768px) {
    h1, .h1 {
      font-size: 56px;
    }
  }
  
  h2, .h2 {
    font-size: 36px;
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: -0.2px;
  }
  
  body, p {
    font-size: 18px;
    line-height: 1.5;
    font-weight: 400;
    letter-spacing: -0.2px;
  }
  
  /* Light grid pattern overlay */
  .light-grid-overlay {
    position: relative;
  }
  
  .light-grid-overlay::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(to right, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                      linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 1;
  }
  
  /* Glass effect panel */
  .glass-panel {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius);
  }
}

@layer base {
  :root {
    /* Black and white color palette */
    --background: 0 0% 100%; /* Pure white */
    --foreground: 0 0% 0%; /* Pure black for text */
    --card: 0 0% 100%; /* Pure white for cards */
    --card-foreground: 0 0% 0%; /* Pure black for card text */
    --popover: 0 0% 100%; /* Pure white for popovers */
    --popover-foreground: 0 0% 0%; /* Pure black for popover text */
    
    /* Primary: Black */
    --primary: 0 0% 0%; 
    --primary-foreground: 0 0% 100%;
    
    /* Secondary: Light gray */
    --secondary: 0 0% 95%;
    --secondary-foreground: 0 0% 0%;
    
    /* Accent: Dark gray */
    --accent: 0 0% 20%;
    --accent-foreground: 0 0% 100%;
    
    --muted: 0 0% 97%;
    --muted-foreground: 0 0% 45%;
    
    --destructive: 0 75% 50%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 217 100% 53%;
    
    /* Chart colors in grayscale */
    --chart-1: 0 0% 0%; /* Black */
    --chart-2: 0 0% 30%; /* Dark gray */
    --chart-3: 0 0% 50%; /* Medium gray */
    --chart-4: 0 0% 70%; /* Light gray */
    --chart-5: 0 0% 90%; /* Very light gray */
    
    --radius: 0.8rem;
    
    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 0 0% 30%;
    --sidebar-primary: 217 100% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 100% 95%;
    --sidebar-accent-foreground: 217 100% 53%;
    --sidebar-border: 0 0% 90%;
    --sidebar-ring: 217 100% 53%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Animations and transitions */
@layer utilities {
  .transition-apple {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.2, 0.8, 0.2, 1);
    transition-duration: 300ms;
  }
  
  .hover-lift {
    transition-property: transform, box-shadow;
    transition-timing-function: cubic-bezier(0.2, 0.8, 0.2, 1);
    transition-duration: 300ms;
  }
  
  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .enterprise-card {
    @apply rounded-2xl bg-white shadow-sm border border-border/40;
    backdrop-filter: blur(20px);
    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  }
  
  .enterprise-card:hover {
    @apply shadow-md;
    transform: translateY(-2px);
  }
  
  /* Gradient buttons */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)/0.8) 100%);
    transition: all 0.3s ease;
  }
  
  .gradient-primary:hover {
    background: linear-gradient(135deg, hsl(var(--primary)/0.9) 0%, hsl(var(--primary)) 100%);
    box-shadow: 0 5px 15px rgba(15, 98, 254, 0.3);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--secondary)/0.8) 100%);
    transition: all 0.3s ease;
  }
  
  .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--accent)/0.8) 100%);
    transition: all 0.3s ease;
  }
  
  /* Flip card animations */
  .flip-card-container {
    perspective: 1000px;
    height: 100%;
    display: flex;
    align-items: stretch;
  }
  
  .flip-card {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;
    transition: transform 0.8s cubic-bezier(0.2, 0.8, 0.2, 1);
    transform-style: preserve-3d;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
    border-radius: 1.5rem;
  }
  
  .flip-card-container:hover .flip-card {
    transform: rotateY(180deg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .flip-card-front, 
  .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    display: flex;
    flex-direction: column;
    border-radius: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08), 0 5px 15px rgba(0, 0, 0, 0.05);
  }
  
  .flip-card-front {
    align-items: center;
    justify-content: center;
  }
  
  .flip-card-back {
    transform: rotateY(180deg);
    justify-content: space-between;
  }
}
