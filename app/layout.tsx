import type React from "react"
import "@/app/globals.css"
import type { Metadata } from "next"
import { Toaster } from "@/components/ui/toaster"

// SF Pro font is loaded via CSS

export const metadata: Metadata = {
  title: {
    default: "Exor Holding",
    template: "%s | Exor Holding",
  },
  description:
    "Exor Holding è il punto di riferimento nei settori edilizia, mobilità e servizi per la Pubblica Amministrazione.",
  keywords: ["Exor Holding", "costruzioni", "noleggio auto", "servizi PA", "edilizia", "mobilità"],
  authors: [
    {
      name: "Exor Holding",
      url: "https://exorholding.it",
    },
  ],
  creator: "Exor Holding",
  openGraph: {
    type: "website",
    locale: "it_IT",
    url: "https://exorholding.it",
    title: "Exor Holding",
    description:
      "Exor Holding è il punto di riferimento nei settori edilizia, mobilità e servizi per la Pubblica Amministrazione.",
    siteName: "Exor Holding",
  },
  twitter: {
    card: "summary_large_image",
    title: "Exor Holding",
    description:
      "Exor Holding è il punto di riferimento nei settori edilizia, mobilità e servizi per la Pubblica Amministrazione.",
    creator: "@exorholding",
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: "https://exorholding.it/site.webmanifest",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="it">
      <head />
      <body className="antialiased">
        {children}
        <Toaster />
      </body>
    </html>
  )
}

